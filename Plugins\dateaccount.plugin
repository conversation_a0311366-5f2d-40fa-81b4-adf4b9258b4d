import requests
import json
import time
import threading
import traceback
import os
from datetime import datetime
from base_plugin import <PERSON><PERSON><PERSON><PERSON>, HookStrategy, BasePlugin, MenuItemData, MenuItemType
from client_utils import send_message, run_on_queue, get_last_fragment, run_on_ui_thread, get_messages_controller
from markdown_utils import parse_markdown
from ui.settings import Header, Input, Switch, Text, Divider
from ui.alert import AlertDialogBuilder
from ui.bulletin import BulletinHelper
from java.util import Locale
from org.telegram.messenger import MessageObject
from org.telegram.ui.ActionBar import AlertDialog
from com.exteragram.messenger.plugins import PluginsController
from com.exteragram.messenger.plugins.ui import PluginSettingsActivity

__id__ = "account_age_checker"
__name__ = "Account Age Checker"
__description__ = "Check the age of a Telegram account. Features free mode (local data only) and API mode with cache, loading indicator, and more. Includes profile button for quick account info access."
__author__ = "@mi<PERSON><PERSON><PERSON><PERSON><PERSON> & @mew_help"
__min_version__ = "11.12.1"
__icon__ = "DateRegBot_by_MoiStikiBot/10"
__version__ = "4.0.0"
__copyright__ = "© 2025. All rights reserved. Copying, modification, or redistribution of this plugin code is strictly prohibited."

AUTOUPDATE_CHANNEL_ID = **********
AUTOUPDATE_CHANNEL_USERNAME = "mishabotov"
AUTOUPDATE_MESSAGE_ID = 35

PREMIUM_EMOJI_MAP = {
    "📊": "[📊](5190806721286657692)",
    "👤": "[👤](5260399854500191689)",
    "📅": "[📅](5890937706803894250)",
    "🎯": "[💯](5206318837489743801)",
    "⏳": "[🕔](5255971360965930740)",
    "⚙️": "[⚙️](5253952855185829086)",
    "🟢": "[✅](5021905410089550576)",
    "🟡": "[⚡️](4997289591011544358)",
    "🟠": "[📺](5350513667144163474)",
    "🔴": "[❗️](4927486932113425461)",
    "😒": "[😒](5303360582706015886)",
    "🔗": "[🔗](5253490441826870592)",
    "🖥": "[📲](***********54022988)",
    "🏳️": "[🏳️](5391183322355357915)",
    "🎉": "[🎉](5357257143756005264)",
    "🪪": "[🪪](5936017305585586269)",
    "⚠️": "[⚠️](5447644880824181073)",
    "🆓": "[🆓](5406756500108501710)"
}

DC_REGION_MAP = {
    1: "Miami, FL",
    2: "Amsterdam, NL",
    3: "Miami, FL",
    4: "Amsterdam, NL",
    5: "Singapore, SG"
}

def replace_with_premium_emoji(text: str) -> str:
    result = text
    for regular_emoji, premium_emoji in PREMIUM_EMOJI_MAP.items():
        result = result.replace(regular_emoji, premium_emoji)
    return result

def get_regular_emoji_for_bulletin(text: str) -> str:
    result = text
    for regular_emoji, premium_emoji in PREMIUM_EMOJI_MAP.items():
        result = result.replace(premium_emoji, regular_emoji)
    return result

def get_account_name_from_telegram(user_id: str) -> str:
    try:
        from client_utils import get_messages_controller
        from org.telegram.messenger import UserObject

        user = get_messages_controller().getUser(int(user_id))
        if user is None:
            return "Неизвестно"

        first_name = ""
        last_name = ""

        if hasattr(user, 'first_name') and user.first_name:
            first_name = user.first_name.strip()

        if hasattr(user, 'last_name') and user.last_name:
            last_name = user.last_name.strip()

        if first_name and last_name:
            full_name = f"{first_name} {last_name}"
        elif first_name:
            full_name = first_name
        elif last_name:
            full_name = last_name
        else:
            full_name = ""

        if full_name and full_name != "DELETED":
            return full_name

        if hasattr(user, 'username') and user.username:
            return f"@{user.username}"

        return "Неизвестно"

    except Exception as e:
        _log(f"Error getting account name from Telegram: {e}")
        return "Неизвестно"

def get_all_usernames_from_telegram(user_id: str) -> dict:
    """
    Получает все юзернеймы пользователя (основной и дополнительные/покупные)
    Возвращает словарь с информацией о юзернеймах
    """
    try:
        from client_utils import get_messages_controller

        user = get_messages_controller().getUser(int(user_id))
        if user is None:
            return {"primary": "", "additional": [], "collectible": []}

        result = {
            "primary": "",
            "additional": [],
            "collectible": []
        }

        # Основной юзернейм
        if hasattr(user, 'username') and user.username:
            result["primary"] = user.username.strip()

        # Дополнительные юзернеймы из user.usernames (Java ArrayList)
        if hasattr(user, 'usernames') and user.usernames:
            try:
                # user.usernames это Java ArrayList, нужно использовать size() и get()
                usernames_count = user.usernames.size()
                for i in range(usernames_count):
                    username_obj = user.usernames.get(i)
                    if (hasattr(username_obj, 'active') and hasattr(username_obj, 'username') and
                        username_obj.active and username_obj.username):
                        username_str = username_obj.username.strip()
                        if username_str:
                            # Покупные юзернеймы: active=True и editable=False
                            if hasattr(username_obj, 'editable') and not username_obj.editable:
                                result["collectible"].append(username_str)
                            else:
                                # Обычные дополнительные юзернеймы
                                result["additional"].append(username_str)
            except Exception as e:
                _log(f"Error iterating usernames ArrayList: {e}")

        return result

    except Exception as e:
        _log(f"Error getting all usernames from Telegram: {e}")
        return {"primary": "", "additional": [], "collectible": []}

def get_username_from_telegram(user_id: str) -> str:
    """
    Получает основной публичный юзернейм пользователя
    Использует логику аналогичную UserObject.getPublicUsername()
    """
    try:
        from client_utils import get_messages_controller

        user = get_messages_controller().getUser(int(user_id))
        if user is None:
            return ""

        # Сначала проверяем основной юзернейм
        if hasattr(user, 'username') and user.username:
            return user.username.strip()

        # Если основного нет, ищем в дополнительных активных юзернеймах (Java ArrayList)
        if hasattr(user, 'usernames') and user.usernames:
            try:
                usernames_count = user.usernames.size()
                for i in range(usernames_count):
                    username_obj = user.usernames.get(i)
                    if (hasattr(username_obj, 'active') and hasattr(username_obj, 'username') and
                        username_obj.active and username_obj.username):
                        return username_obj.username.strip()
            except Exception as e:
                _log(f"Error iterating usernames ArrayList in get_username_from_telegram: {e}")

        return ""

    except Exception as e:
        _log(f"Error getting username from Telegram: {e}")
        return ""

def get_user_phone_from_telegram(user_id: str) -> str:
    try:
        from client_utils import get_messages_controller

        user = get_messages_controller().getUser(int(user_id))
        if user is None:
            _log(f"User {user_id} not found")
            return ""

        if hasattr(user, 'phone') and user.phone:
            phone = user.phone.strip()
            if phone:

                if not phone.startswith("+"):
                    phone = "+" + phone
                _log(f"Phone found for user {user_id}: {phone}")
                return phone

        _log(f"No phone found for user {user_id}")
        return ""

    except Exception as e:
        _log(f"Error getting phone from Telegram for user {user_id}: {e}")
        return ""

def get_user_birthday_from_telegram(user_id: str) -> str:
    try:
        from client_utils import get_messages_controller


        user_full = get_messages_controller().getUserFull(int(user_id))
        if user_full is None:
            _log(f"UserFull not found for user {user_id}")
            return ""


        if not hasattr(user_full, 'birthday') or user_full.birthday is None:
            _log(f"No birthday found for user {user_id}")
            return ""

        birthday = user_full.birthday


        if not hasattr(birthday, 'day') or not hasattr(birthday, 'month'):
            _log(f"Invalid birthday structure for user {user_id}")
            return ""

        day = birthday.day
        month = birthday.month


        if day < 1 or day > 31 or month < 1 or month > 12:
            _log(f"Invalid birthday values for user {user_id}: day={day}, month={month}")
            return ""


        months_ru = [
            "", "января", "февраля", "марта", "апреля", "мая", "июня",
            "июля", "августа", "сентября", "октября", "ноября", "декабря"
        ]
        months_en = [
            "", "January", "February", "March", "April", "May", "June",
            "July", "August", "September", "October", "November", "December"
        ]


        current_language = Locale.getDefault().getLanguage()
        month_names = months_ru if current_language == "ru" else months_en
        month_name = month_names[month] if month < len(month_names) else str(month)


        if hasattr(birthday, 'flags') and hasattr(birthday, 'year') and (birthday.flags & 1) != 0:
            year = birthday.year
            if year > 0:
                _log(f"Birthday found for user {user_id}: {day} {month_name} {year}")
                return f"{day} {month_name} {year}"


        _log(f"Birthday found for user {user_id} (no year): {day} {month_name}")
        return f"{day} {month_name}"

    except Exception as e:
        _log(f"Error getting birthday from Telegram for user {user_id}: {e}")
        return ""

def get_user_datacenter_id(user_id: str) -> int:
    try:
        from client_utils import get_messages_controller


        user = get_messages_controller().getUser(int(user_id))
        if user is None:
            _log(f"User {user_id} not found in cache")
            return 0


        if hasattr(user, 'photo') and user.photo and hasattr(user.photo, 'dc_id'):
            dc_id = user.photo.dc_id
            _log(f"User {user_id} datacenter from user photo: {dc_id}")
            return dc_id


        try:
            user_full = get_messages_controller().getUserFull(int(user_id))
            if user_full:

                if hasattr(user_full, 'profile_photo') and user_full.profile_photo:
                    if hasattr(user_full.profile_photo, 'dc_id'):
                        dc_id = user_full.profile_photo.dc_id
                        _log(f"User {user_id} datacenter from full profile photo: {dc_id}")
                        return dc_id


                if hasattr(user_full, 'personal_photo') and user_full.personal_photo:
                    if hasattr(user_full.personal_photo, 'dc_id'):
                        dc_id = user_full.personal_photo.dc_id
                        _log(f"User {user_id} datacenter from personal photo: {dc_id}")
                        return dc_id


                if hasattr(user_full, 'fallback_photo') and user_full.fallback_photo:
                    if hasattr(user_full.fallback_photo, 'dc_id'):
                        dc_id = user_full.fallback_photo.dc_id
                        _log(f"User {user_id} datacenter from fallback photo: {dc_id}")
                        return dc_id

        except Exception as e:
            _log(f"Could not get full user info: {e}")


        user_id_int = int(user_id)


        if user_id_int < 10000000:
            estimated_dc = 2
        elif user_id_int < 100000000:
            estimated_dc = 1
        elif user_id_int < 500000000:
            estimated_dc = 2
        elif user_id_int < 1000000000:
            estimated_dc = 4
        elif user_id_int < **********:
            estimated_dc = 5
        else:
            estimated_dc = 5

        _log(f"Estimated datacenter for user {user_id}: {estimated_dc} (heuristic, may be inaccurate)")
        return estimated_dc

    except Exception as e:
        _log(f"Error getting datacenter ID for user {user_id}: {e}")
        return 0

def get_region_from_datacenter(dc_id: int) -> str:
    return DC_REGION_MAP.get(dc_id, "Неизвестно")

def _log(message: str):
    if AccountAgePlugin.is_debug_globally_enabled():
        from android_utils import log as extera_log
        extera_log(f"[{__name__}] {message}")

class CacheManager:
    def __init__(self, plugin_instance):
        self.plugin = plugin_instance
        self.cache_file = self._get_cache_file_instance()
        self.cache = self.cache_file.content

    def _get_cache_file_instance(self):
        try:
            from zwylib import JsonCacheFile
            _log("Using ZwyLib's JsonCacheFile for caching.")
            return JsonCacheFile(f"{__id__}_cache.json", {})
        except ImportError:
            _log("ZwyLib not found. Using local JsonCacheFile implementation.")
            class LocalJsonCacheFile:
                cache_dir_name = os.path.join(os.path.dirname(os.path.realpath(__file__)), "cache")
                def __init__(self, filename: str, default: dict):
                    self.filename = filename
                    self.path = os.path.join(self.cache_dir_name, filename)
                    self.content = default.copy()
                    self.default = default.copy()
                    os.makedirs(self.cache_dir_name, exist_ok=True)
                    self.read()
                def read(self):
                    try:
                        with open(self.path, 'r', encoding='utf-8') as f: self.content = json.load(f)
                    except: self.wipe()
                def write(self):
                    try:
                        with open(self.path, 'w', encoding='utf-8') as f: json.dump(self.content, f, ensure_ascii=False, indent=4)
                    except Exception as e: _log(f"Cache write error: {e}")
                def wipe(self):
                    self.content = self.default.copy()
                    self.write()
            return LocalJsonCacheFile(f"{__id__}_cache.json", {})

    def get(self, user_id: str, method: str):
        cache_duration = int(self.plugin.get_setting("cache_duration", "86400"))
        cache_key = f"{user_id}_{method}"

        if cache_key in self.cache:
            cached_data = self.cache[cache_key]
            if (time.time() - cached_data.get("timestamp", 0)) < cache_duration:
                _log(f"Cache hit for user_id: {user_id}, method: {method}")
                return cached_data.get("data")
        _log(f"Cache miss for user_id: {user_id}, method: {method}")
        return None

    def set(self, user_id: str, method: str, data: dict):
        cache_key = f"{user_id}_{method}"
        self.cache[cache_key] = {
            "timestamp": time.time(),
            "data": data
        }
        self.cache_file.write()
        _log(f"Saved to cache for user_id: {user_id}, method: {method}")

    def delete(self, user_id: str):
        user_id_str = str(user_id)
        keys_to_delete = [
            key for key in self.cache
            if key.startswith(f"{user_id_str}_")
        ]

        if not keys_to_delete:
            _log(f"No cache entries found to delete for user_id: {user_id_str}")
            return False

        for key in keys_to_delete:
            del self.cache[key]

        self.cache_file.write()
        _log(f"Deleted {len(keys_to_delete)} cache entries for user_id: {user_id_str}")
        return True

    def prune_expired(self):
        cache_duration = int(self.plugin.get_setting("cache_duration", "86400"))
        now = time.time()
        keys_to_delete = [
            key for key, value in self.cache.items()
            if (now - value.get("timestamp", 0)) >= cache_duration
        ]
        if not keys_to_delete: return
        for key in keys_to_delete: del self.cache[key]
        self.cache_file.write()
        _log(f"Pruned {len(keys_to_delete)} expired entries from cache.")

    def clear(self):
        self.cache_file.wipe()
        self.cache = self.cache_file.content
        _log("Cache cleared by user.")


class AgeCheckerAPI:
    DEFAULT_BASE_URL = "https://api.goy.guru/api/v1"
    def __init__(self, api_key, custom_url=None):
        self.api_key = api_key
        self.base_url = custom_url.strip().rstrip('/') if custom_url else self.DEFAULT_BASE_URL
        self.session = requests.Session()
        self.session.headers.update({"User-Agent": f"ExteraPlugin/{__id__}/{__version__}"})

    def _handle_request_exception(self, e, context):
        _log(f"Network error during {context}: {e}")
        return {"success": False, "error": f"Network error: {e}"}

    def _handle_api_error(self, response, context):
        error_message = f"API request failed for {context} with status code {response.status_code}: {response.text}"
        try: error_data = response.json(); error_message = error_data.get('detail', error_message)
        except: pass
        if response.status_code == 401: error_message = "Invalid API key."
        elif response.status_code == 402: error_message = "Insufficient balance."
        elif response.status_code == 404: error_message = "User not found."
        _log(error_message)
        return {"success": False, "error": error_message}

    def resolve_username(self, username):
        _log(f"Resolving username '{username}'...")
        endpoint = f"{self.base_url}/users/resolveUsername"
        params = {"token": self.api_key, "username": username.lstrip('@')}
        try:
            response = self.session.get(endpoint, params=params, timeout=20)
            if response.status_code == 200:
                data = response.json()
                if data.get('id'):
                    _log(f"Username '{username}' resolved to ID: {data['id']}")
                    return {"success": True, "data": data}
                return {"success": False, "error": "Could not resolve username."}
            return self._handle_api_error(response, f"resolve_username for '{username}'")
        except requests.exceptions.RequestException as e:
            return self._handle_request_exception(e, f"resolve_username for '{username}'")

    def get_info(self, user_id, method="fast"):
        _log(f"Requesting creation date for ID '{user_id}' with method '{method}'")
        endpoint = f"{self.base_url}/users/getCreationDateSmart" if method == "smart" else f"{self.base_url}/users/getCreationDateFast"
        params = {"token": self.api_key, "user_id": user_id}
        try:
            response = self.session.get(endpoint, params=params, timeout=20)
            if response.status_code == 200:
                data = response.json()
                if data.get('creation_date'):
                    _log(f"API success for '{user_id}': {data}")
                    return {"success": True, "data": data}
                _log(f"API error for '{user_id}': Creation date not found in response.")
                return {"success": False, "error": data.get("error", "Creation date not found")}
            return self._handle_api_error(response, f"get_info for '{user_id}'")
        except requests.exceptions.RequestException as e:
            return self._handle_request_exception(e, f"get_info for '{user_id}'")


class LocalizationManager:
    def __init__(self):
        self.language = Locale.getDefault().getLanguage()
        self.language = self.language if self.language in self._get_supported_languages() else "en"
    def get_string(self, string): return self.strings[self.language][string]
    def _get_supported_languages(self): return self.strings.keys()
    strings = {
        "ru": {
            "USAGE_EXAMPLE": "⚠️ *Использование:* `.age [имя пользователя/ID]`\n`.age smart [имя пользователя/ID]` для более точного анализа",
            "API_ERROR": "⛔ *Произошла ошибка при запросе к API:*\n\n`{0}`",
            "RESULT": """*📊 ИНФОРМАЦИЯ ОБ АККАУНТЕ*

*😒 Имя аккаунта:* `{7}`
*👤 ID:* `{0}`
*🖥 DC:* `{9}`
*🏳️ Регион:* `{10}`
*🔗 Юзернейм:* @{8}
*📅 Дата создания:* `{2}`
*🎯 Точность:* {5} {3}
*⏳ Возраст аккаунта:* `{4}`
*⚙️ Метод:* `{6}`""",

            "RESULT_WITH_PHONE": """*📊 ИНФОРМАЦИЯ ОБ АККАУНТЕ*

*😒 Имя аккаунта:* `{7}`
*👤 ID:* `{0}`
*🖥 DC:* `{9}`
*🏳️ Регион:* `{10}`
*🔗 Юзернейм:* @{8}
*🪪 Номер телефона:* `{12}`
*📅 Дата создания:* `{2}`
*🎯 Точность:* {5} {3}
*⏳ Возраст аккаунта:* `{4}`
*⚙️ Метод:* `{6}`""",

            "RESULT_WITH_BIRTHDAY": """*📊 ИНФОРМАЦИЯ ОБ АККАУНТЕ*

*😒 Имя аккаунта:* `{7}`
*👤 ID:* `{0}`
*🖥 DC:* `{9}`
*🏳️ Регион:* `{10}`
*🔗 Юзернейм:* @{8}
*🎉 Дата рождения:* `{11}`
*📅 Дата создания:* `{2}`
*🎯 Точность:* {5} {3}
*⏳ Возраст аккаунта:* `{4}`
*⚙️ Метод:* `{6}`""",
            "RESULT_WITH_BIRTHDAY_AND_PHONE": """*📊 ИНФОРМАЦИЯ ОБ АККАУНТЕ*

*😒 Имя аккаунта:* `{7}`
*👤 ID:* `{0}`
*🖥 DC:* `{9}`
*🏳️ Регион:* `{10}`
*🔗 Юзернейм:* @{8}
*🪪 Номер телефона:* `{12}`
*🎉 Дата рождения:* `{11}`
*📅 Дата создания:* `{2}`
*🎯 Точность:* {5} {3}
*⏳ Возраст аккаунта:* `{4}`
*⚙️ Метод:* `{6}`""",
            "RESULT_NO_USERNAME": """*📊 ИНФОРМАЦИЯ ОБ АККАУНТЕ*

*😒 Имя аккаунта:* `{7}`
*👤 ID:* `{0}`
*🖥 DC:* `{9}`
*🏳️ Регион:* `{10}`
*🔗 Юзернейм:* @{8}
*📅 Дата создания:* `{2}`
*🎯 Точность:* {5} {3}
*⏳ Возраст аккаунта:* `{4}`
*⚙️ Метод:* `{6}`""",
            "RESULT_NO_USERNAME_WITH_PHONE": """*📊 ИНФОРМАЦИЯ ОБ АККАУНТЕ*

*😒 Имя аккаунта:* `{7}`
*👤 ID:* `{0}`
*🖥 DC:* `{9}`
*🏳️ Регион:* `{10}`
*🔗 Юзернейм:* @{8}
*🪪 Номер телефона:* `{12}`
*📅 Дата создания:* `{2}`
*🎯 Точность:* {5} {3}
*⏳ Возраст аккаунта:* `{4}`
*⚙️ Метод:* `{6}`""",
            "RESULT_NO_USERNAME_WITH_BIRTHDAY": """*📊 ИНФОРМАЦИЯ ОБ АККАУНТЕ*

*😒 Имя аккаунта:* `{7}`
*👤 ID:* `{0}`
*🖥 DC:* `{9}`
*🏳️ Регион:* `{10}`
*🔗 Юзернейм:* @{8}
*🎉 Дата рождения:* `{11}`
*📅 Дата создания:* `{2}`
*🎯 Точность:* {5} {3}
*⏳ Возраст аккаунта:* `{4}`
*⚙️ Метод:* `{6}`""",
            "RESULT_NO_USERNAME_WITH_BIRTHDAY_AND_PHONE": """*📊 ИНФОРМАЦИЯ ОБ АККАУНТЕ*

*😒 Имя аккаунта:* `{7}`
*👤 ID:* `{0}`
*🖥 DC:* `{9}`
*🏳️ Регион:* `{10}`
*🔗 Юзернейм:* @{8}
*🪪 Номер телефона:* `{12}`
*🎉 Дата рождения:* `{11}`
*📅 Дата создания:* `{2}`
*🎯 Точность:* {5} {3}
*⏳ Возраст аккаунта:* `{4}`
*⚙️ Метод:* `{6}`""",
            "CODE_ERROR": "⛔ *Произошла ошибка в коде плагина:*\n\n`{0}`",
            "API_KEY_TITLE": "Настройки API",
            "API_KEY_DESCRIPTION": "Введите ваш API ключ для доступа к API. Получите ключ в боте @dateregbot",
            "NO_API_KEY": "⚠️ *API ключ не настроен.* Получите API ключ в боте @dateregbot и укажите его в настройках плагина.",
            "CUSTOM_API_URL_TITLE": "URL API",
            "CUSTOM_API_URL_DESCRIPTION": "Укажите собственный URL API (оставьте пустым для использования по умолчанию)",
            "FAQ_COMMAND": "faq",
            "FAQ_CONTENT": """❓ ЧАСТО ЗАДАВАЕМЫЕ ВОПРОСЫ (FAQ)

1. Что делает этот плагин?
Плагин проверяет примерную дату создания аккаунта Telegram по юзернейму или ID.

2. Как использовать этот плагин?
Используйте команду .age [юзернейм/ID] или ответьте на сообщение командой .age
Для более точного анализа используйте: .age smart [юзернейм/ID]

3. Почему нужен API ключ?
API ключ необходим для доступа к сервису, который определяет дату создания аккаунта.

4. Где получить API ключ?
API ключ можно получить в боте @dateregbot

5. Насколько точны результаты?
Точность указывается в каждом результате и зависит от многих факторов.
🟢 - высокая точность (87-100%)
🟡 - средняя точность (70-86%)
🟠 - низкая точность (50-69%)
🔴 - очень низкая точность (<50%)

6. Что означает "точность" в результатах?
Это показатель надежности определения даты создания аккаунта.

7. Есть ли лимиты на использование?
Да, лимиты зависят от вашего API ключа и тарифа у @dateregbot.

8. Чем отличаются методы Fast и Smart?
Fast - быстрый, но менее точный метод
Smart - более точный, но более медленный метод""",
            "PROCESSING": "Пожалуйста, подождите, предыдущий запрос еще выполняется...",
            "LOADING_TITLE": "Проверка возраста аккаунта...",
            "CACHED_RESULT_HINT": "_(Результат из кэша)_",
            "SETTINGS_DEBUG_MODE": "Режим отладки",
            "SETTINGS_DEBUG_MODE_SUBTEXT": "Включает подробные логи в logcat для помощи в решении проблем.",
            "SETTINGS_CACHE_DURATION": "Время жизни кэша (в секундах)",
            "SETTINGS_CACHE_DURATION_SUBTEXT": "Как долго хранить результаты. 86400 = 24 часа.",
            "SETTINGS_CLEAR_CACHE": "Очистить кэш",
            "SETTINGS_CACHE_CLEARED": "Кэш успешно очищен.",
            "CACHE_DELETE_SUCCESS": "Кэш для ID `{0}` успешно очищен.",
            "CACHE_DELETE_NOT_FOUND": "Кэш для ID `{0}` не найден.",
            "CACHE_DELETE_USAGE": "Использование: .cache <ID>",
            "FAQ_TITLE": "❓ FAQ",
            "SETTINGS_MENU_BUTTON": "Кнопка настроек в меню",
            "SETTINGS_SHOW_BUTTON_DESC": "Добавляет кнопку открытия настроек плагина в меню",
            "SETTINGS_TITLE": "Настройки Account Age",
            "USE_PREMIUM_EMOJI_TITLE": "Премиум эмодзи",
            "USE_PREMIUM_EMOJI_SUBTEXT": "Заменять обычные эмодзи на анимированные премиум эмодзи в результатах.",
            "DONATE_TITLE": "Поддержать разработку",
            "DONATE_INFO": "Другая информация и реквизиты",
            "FREE_MODE_TITLE": "Режим использования",
            "FREE_MODE_SUBTEXT": "Бесплатный режим показывает только локальные данные (без даты создания)",
            "FREE_MODE_RESULT": """*📊 ИНФОРМАЦИЯ ОБ АККАУНТЕ:* (🆓)

*😒 Имя аккаунта:* `{0}`
*👤 ID:* `{1}`
*🖥 DC:* `{2}`
*🏳️ Регион:* `{3}`
*🔗 Юзернейм:* @{4}""",

            "FREE_MODE_RESULT_WITH_PHONE": """*📊 ИНФОРМАЦИЯ ОБ АККАУНТЕ:* (🆓)

*😒 Имя аккаунта:* `{0}`
*👤 ID:* `{1}`
*🖥 DC:* `{2}`
*🏳️ Регион:* `{3}`
*🔗 Юзернейм:* @{4}
*🪪 Номер телефона:* `{5}`""",
            "FREE_MODE_RESULT_WITH_BIRTHDAY": """*📊 ИНФОРМАЦИЯ ОБ АККАУНТЕ:* (🆓)

*😒 Имя аккаунта:* `{0}`
*👤 ID:* `{1}`
*🖥 DC:* `{2}`
*🏳️ Регион:* `{3}`
*🔗 Юзернейм:* @{4}
*🎉 Дата рождения:* `{5}`""",
            "FREE_MODE_RESULT_WITH_BIRTHDAY_AND_PHONE": """*📊 ИНФОРМАЦИЯ ОБ АККАУНТЕ:* (🆓)

*😒 Имя аккаунта:* `{0}`
*👤 ID:* `{1}`
*🖥 DC:* `{2}`
*🏳️ Регион:* `{3}`
*🔗 Юзернейм:* @{4}
*🪪 Номер телефона:* `{5}`
*🎉 Дата рождения:* `{6}`""",
            "FREE_MODE_NO_USERNAME": """*📊 ИНФОРМАЦИЯ ОБ АККАУНТЕ:* (🆓)

*😒 Имя аккаунта:* `{0}`
*👤 ID:* `{1}`
*🖥 DC:* `{2}`
*🏳️ Регион:* `{3}`
*🔗 Юзернейм:* отсутствует""",
            "FREE_MODE_NO_USERNAME_WITH_PHONE": """*📊 ИНФОРМАЦИЯ ОБ АККАУНТЕ:* (🆓)

*😒 Имя аккаунта:* `{0}`
*👤 ID:* `{1}`
*🖥 DC:* `{2}`
*🏳️ Регион:* `{3}`
*🔗 Юзернейм:* отсутствует
*🪪 Номер телефона:* `{4}`""",
            "FREE_MODE_NO_USERNAME_WITH_BIRTHDAY": """*📊 ИНФОРМАЦИЯ ОБ АККАУНТЕ:* (🆓)

*😒 Имя аккаунта:* `{0}`
*👤 ID:* `{1}`
*🖥 DC:* `{2}`
*🏳️ Регион:* `{3}`
*🔗 Юзернейм:* отсутствует
*🎉 Дата рождения:* `{4}`""",
            "FREE_MODE_NO_USERNAME_WITH_BIRTHDAY_AND_PHONE": """*📊 ИНФОРМАЦИЯ ОБ АККАУНТЕ:* (🆓)

*😒 Имя аккаунта:* `{0}`
*👤 ID:* `{1}`
*🖥 DC:* `{2}`
*🏳️ Регион:* `{3}`
*🔗 Юзернейм:* отсутствует
*🪪 Номер телефона:* `{4}`
*🎉 Дата рождения:* `{5}`""",
        },
        "en": {
            "USAGE_EXAMPLE": "⚠️ *Usage:* `.age [username/ID]`\n`.age smart [username/ID]` for more accurate analysis",
            "API_ERROR": "⛔ *An error occurred while querying the API:*\n\n`{0}`",
            "RESULT": """*📊 ACCOUNT INFORMATION*

*😒 Account Name:* `{7}`
*👤 ID:* `{0}`
*🖥 DC:* `{9}`
*🏳️ Region:* `{10}`
*🔗 Username:* @{8}
*📅 Creation Date:* `{2}`
*🎯 Accuracy:* {5} {3}
*⏳ Account Age:* `{4}`
*⚙️ Method:* `{6}`""",

            "RESULT_WITH_PHONE": """*📊 ACCOUNT INFORMATION*

*😒 Account Name:* `{7}`
*👤 ID:* `{0}`
*🖥 DC:* `{9}`
*🏳️ Region:* `{10}`
*🔗 Username:* @{8}
*🪪 Phone Number:* `{12}`
*📅 Creation Date:* `{2}`
*🎯 Accuracy:* {5} {3}
*⏳ Account Age:* `{4}`
*⚙️ Method:* `{6}`""",

            "RESULT_WITH_BIRTHDAY": """*📊 ACCOUNT INFORMATION*

*😒 Account Name:* `{7}`
*👤 ID:* `{0}`
*🖥 DC:* `{9}`
*🏳️ Region:* `{10}`
*🔗 Username:* @{8}
*🎉 Birthday:* `{11}`
*📅 Creation Date:* `{2}`
*🎯 Accuracy:* {5} {3}
*⏳ Account Age:* `{4}`
*⚙️ Method:* `{6}`""",
            "RESULT_WITH_BIRTHDAY_AND_PHONE": """*📊 ACCOUNT INFORMATION*

*😒 Account Name:* `{7}`
*👤 ID:* `{0}`
*🖥 DC:* `{9}`
*🏳️ Region:* `{10}`
*🔗 Username:* @{8}
*🪪 Phone Number:* `{12}`
*🎉 Birthday:* `{11}`
*📅 Creation Date:* `{2}`
*🎯 Accuracy:* {5} {3}
*⏳ Account Age:* `{4}`
*⚙️ Method:* `{6}`""",
            "RESULT_NO_USERNAME": """*📊 ACCOUNT INFORMATION*

*😒 Account Name:* `{7}`
*👤 ID:* `{0}`
*🖥 DC:* `{9}`
*🏳️ Region:* `{10}`
*🔗 Username:* @{8}
*📅 Creation Date:* `{2}`
*🎯 Accuracy:* {5} {3}
*⏳ Account Age:* `{4}`
*⚙️ Method:* `{6}`""",
            "RESULT_NO_USERNAME_WITH_PHONE": """*📊 ACCOUNT INFORMATION*

*😒 Account Name:* `{7}`
*👤 ID:* `{0}`
*🖥 DC:* `{9}`
*🏳️ Region:* `{10}`
*🔗 Username:* @{8}
*🪪 Phone Number:* `{12}`
*📅 Creation Date:* `{2}`
*🎯 Accuracy:* {5} {3}
*⏳ Account Age:* `{4}`
*⚙️ Method:* `{6}`""",
            "RESULT_NO_USERNAME_WITH_BIRTHDAY": """*📊 ACCOUNT INFORMATION*

*😒 Account Name:* `{7}`
*👤 ID:* `{0}`
*🖥 DC:* `{9}`
*🏳️ Region:* `{10}`
*🔗 Username:* @{8}
*🎉 Birthday:* `{11}`
*📅 Creation Date:* `{2}`
*🎯 Accuracy:* {5} {3}
*⏳ Account Age:* `{4}`
*⚙️ Method:* `{6}`""",
            "RESULT_NO_USERNAME_WITH_BIRTHDAY_AND_PHONE": """*📊 ACCOUNT INFORMATION*

*😒 Account Name:* `{7}`
*👤 ID:* `{0}`
*🖥 DC:* `{9}`
*🏳️ Region:* `{10}`
*🔗 Username:* @{8}
*🪪 Phone Number:* `{12}`
*🎉 Birthday:* `{11}`
*📅 Creation Date:* `{2}`
*🎯 Accuracy:* {5} {3}
*⏳ Account Age:* `{4}`
*⚙️ Method:* `{6}`""",
            "CODE_ERROR": "⛔ *An error occurred in the plugin code:*\n\n`{0}`",
            "API_KEY_TITLE": "API Settings",
            "API_KEY_DESCRIPTION": "Enter your API key for API access. Get a key from @dateregbot",
            "NO_API_KEY": "⚠️ *API key not configured.* Get an API key from @dateregbot and set it in the plugin settings.",
            "CUSTOM_API_URL_TITLE": "API URL",
            "CUSTOM_API_URL_DESCRIPTION": "Specify custom API URL (leave empty to use default)",
            "FAQ_COMMAND": "faq",
            "FAQ_CONTENT": """❓ FREQUENTLY ASKED QUESTIONS (FAQ)

1. What does this plugin do?
This plugin checks the approximate creation date of a Telegram account by username or ID.

2. How do I use this plugin?
Use the command .age [username/ID] or reply to a message with .age
For more accurate analysis use: .age smart [username/ID]

3. Why do I need an API key?
The API key is required to access the service that determines account creation dates.

4. How do I get an API key?
You can obtain an API key from the bot @dateregbot

5. How accurate are the results?
Accuracy is indicated in each result and depends on various factors.
🟢 - high accuracy (87-100%)
🟡 - medium accuracy (70-86%)
🟠 - low accuracy (50-69%)
🔴 - very low accuracy (<50%)

6. What does "accuracy" in the results mean?
It's an indicator of how reliable the account creation date determination is.

7. Are there any usage limits?
Yes, limits depend on your API key and service plan from @dateregbot.

8. What's the difference between Fast and Smart methods?
Fast - quick but less accurate method
Smart - more accurate but slower method""",
            "PROCESSING": "Please wait, the previous request is still being processed...",
            "LOADING_TITLE": "Checking Account Age...",
            "CACHED_RESULT_HINT": "_(Result from cache)_",
            "SETTINGS_DEBUG_MODE": "Debug Mode",
            "SETTINGS_DEBUG_MODE_SUBTEXT": "Enables detailed logs in logcat to help with troubleshooting.",
            "SETTINGS_CACHE_DURATION": "Cache Lifetime (in seconds)",
            "SETTINGS_CACHE_DURATION_SUBTEXT": "How long to store results. 86400 = 24 hours.",
            "SETTINGS_CLEAR_CACHE": "Clear Cache",
            "SETTINGS_CACHE_CLEARED": "Cache cleared successfully.",
            "CACHE_DELETE_SUCCESS": "Cache for ID `{0}` cleared successfully.",
            "CACHE_DELETE_NOT_FOUND": "No cache found for ID `{0}`.",
            "CACHE_DELETE_USAGE": "Usage: .cache <ID>",
            "FAQ_TITLE": "❓ FAQ",
            "SETTINGS_MENU_BUTTON": "Settings button in menu",
            "SETTINGS_SHOW_BUTTON_DESC": "Adds plugin settings button to menu",
            "SETTINGS_TITLE": "Account Age Settings",
            "USE_PREMIUM_EMOJI_TITLE": "Premium emoji",
            "USE_PREMIUM_EMOJI_SUBTEXT": "Replace regular emoji with animated premium emoji in results.",
            "DONATE_TITLE": "Support development",
            "DONATE_INFO": "Other info and requisites",
            "FREE_MODE_TITLE": "Usage Mode",
            "FREE_MODE_SUBTEXT": "Free mode shows only local data (without creation date)",
            "FREE_MODE_RESULT": """*📊 ACCOUNT INFORMATION:* (🆓)

*😒 Account Name:* `{0}`
*👤 ID:* `{1}`
*🖥 DC:* `{2}`
*🏳️ Region:* `{3}`
*🔗 Username:* @{4}""",

            "FREE_MODE_RESULT_WITH_PHONE": """*📊 ACCOUNT INFORMATION:* (🆓)

*😒 Account Name:* `{0}`
*👤 ID:* `{1}`
*🖥 DC:* `{2}`
*🏳️ Region:* `{3}`
*🔗 Username:* @{4}
*🪪 Phone Number:* `{5}`""",
            "FREE_MODE_RESULT_WITH_BIRTHDAY": """*📊 ACCOUNT INFORMATION:* (🆓)

*😒 Account Name:* `{0}`
*👤 ID:* `{1}`
*🖥 DC:* `{2}`
*🏳️ Region:* `{3}`
*🔗 Username:* @{4}
*🎉 Birthday:* `{5}`""",
            "FREE_MODE_RESULT_WITH_BIRTHDAY_AND_PHONE": """*📊 ACCOUNT INFORMATION:* (🆓)

*😒 Account Name:* `{0}`
*👤 ID:* `{1}`
*🖥 DC:* `{2}`
*🏳️ Region:* `{3}`
*🔗 Username:* @{4}
*🪪 Phone Number:* `{5}`
*🎉 Birthday:* `{6}`""",
            "FREE_MODE_NO_USERNAME": """*📊 ACCOUNT INFORMATION:* (🆓)

*😒 Account Name:* `{0}`
*👤 ID:* `{1}`
*🖥 DC:* `{2}`
*🏳️ Region:* `{3}`
*🔗 Username:* not available""",
            "FREE_MODE_NO_USERNAME_WITH_PHONE": """*📊 ACCOUNT INFORMATION:* (🆓)

*😒 Account Name:* `{0}`
*👤 ID:* `{1}`
*🖥 DC:* `{2}`
*🏳️ Region:* `{3}`
*🔗 Username:* not available
*🪪 Phone Number:* `{4}`""",
            "FREE_MODE_NO_USERNAME_WITH_BIRTHDAY": """*📊 ACCOUNT INFORMATION:* (🆓)

*😒 Account Name:* `{0}`
*👤 ID:* `{1}`
*🖥 DC:* `{2}`
*🏳️ Region:* `{3}`
*🔗 Username:* not available
*🎉 Birthday:* `{4}`""",
            "FREE_MODE_NO_USERNAME_WITH_BIRTHDAY_AND_PHONE": """*📊 ACCOUNT INFORMATION:* (🆓)

*😒 Account Name:* `{0}`
*👤 ID:* `{1}`
*🖥 DC:* `{2}`
*🏳️ Region:* `{3}`
*🔗 Username:* not available
*🪪 Phone Number:* `{4}`
*🎉 Birthday:* `{5}`""",
        }
    }
locali = LocalizationManager()


class AccountAgePlugin(BasePlugin):
    _debug_enabled = False

    def __init__(self):
        super().__init__()
        self.is_processing = False
        self.api_client = None
        self.alert_dialog = None
        self.cache_manager = CacheManager(self)
        self._drawer_settings_item = None
        self._chat_settings_item = None
        self.profile_menu_item_id = None

    @classmethod
    def is_debug_globally_enabled(cls):
        return cls._debug_enabled

    def on_plugin_load(self):
        self.add_on_send_message_hook()
        self.load_config()
        self.cache_manager.prune_expired()
        _log("Plugin loaded and cache pruned.")
        try:
            import zwylib
            zwylib.add_autoupdater_task(__id__, AUTOUPDATE_CHANNEL_ID, AUTOUPDATE_CHANNEL_USERNAME, AUTOUPDATE_MESSAGE_ID)
            _log("ZwyLib autoupdater task added.")
        except ImportError:
            _log("ZwyLib not found, autoupdater is disabled.")
        try:
            if self.get_setting("show_settings_buttons", True):
                self._add_settings_menu_items()
        except Exception as e:
            _log(f"Failed to add settings menu items on load: {e}")


        try:
            self.profile_menu_item_id = self.add_menu_item(
                MenuItemData(
                    menu_type=MenuItemType.PROFILE_ACTION_MENU,
                    item_id="account_info_button",
                    text="Информация об аккаунте",
                    icon="msg_info",
                    on_click=self._handle_profile_info_click,
                    condition="user != null"
                )
            )
            _log("Profile menu item added successfully.")
        except Exception as e:
            _log(f"Failed to add profile menu item: {e}")

    def on_plugin_unload(self):
        try:
            import zwylib
            zwylib.remove_autoupdater_task(__id__)
            _log("ZwyLib autoupdater task removed.")
        except ImportError: pass
        try:
            if self._drawer_settings_item: self.remove_menu_item(self._drawer_settings_item)
            if self._chat_settings_item: self.remove_menu_item(self._chat_settings_item)
            if self.profile_menu_item_id: self.remove_menu_item(self.profile_menu_item_id)
        except Exception as e: _log(f"Error removing menu items on unload: {e}")
        _log("Plugin unloaded.")

    def load_config(self, _=None):
        AccountAgePlugin._debug_enabled = self.get_setting("debug_mode", False)
        api_key = self.get_setting("api_key", "").strip()
        custom_url = self.get_setting("custom_api_url", "").strip()
        if api_key: self.api_client = AgeCheckerAPI(api_key, custom_url); _log("API client configured.")
        else: self.api_client = None; _log("API key is not set. API client is disabled.")

    def create_settings(self):
        return [
            Header(locali.get_string("FREE_MODE_TITLE")),
            Switch(key="free_mode", text=locali.get_string("FREE_MODE_TITLE"), subtext=locali.get_string("FREE_MODE_SUBTEXT"), default=True, on_change=self.load_config, icon="msg_calendar_solar"),
            Divider(),
            Header(locali.get_string("API_KEY_TITLE")),
            Input(key="api_key", text=locali.get_string("API_KEY_TITLE"), subtext=locali.get_string("API_KEY_DESCRIPTION"), default="", on_change=self.load_config, icon="msg_pin_code"),
            Input(key="custom_api_url", text=locali.get_string("CUSTOM_API_URL_TITLE"), subtext=locali.get_string("CUSTOM_API_URL_DESCRIPTION"), default="", on_change=self.load_config, icon="msg_link"),
            Divider(), Header("Cache & Debug"),
            Input(key="cache_duration", text=locali.get_string("SETTINGS_CACHE_DURATION"), subtext=locali.get_string("SETTINGS_CACHE_DURATION_SUBTEXT"), default="86400", icon="msg_stories_timer"),
            Text(text=locali.get_string("SETTINGS_CLEAR_CACHE"), icon="msg_delete", on_click=self._clear_cache),
            Switch(key="debug_mode", text=locali.get_string("SETTINGS_DEBUG_MODE"), subtext=locali.get_string("SETTINGS_DEBUG_MODE_SUBTEXT"), default=False, on_change=self.load_config, icon="msg_log"),
            Switch(key="show_settings_buttons", text=locali.get_string("SETTINGS_MENU_BUTTON"), icon="msg_reorder", default=True, subtext=locali.get_string("SETTINGS_SHOW_BUTTON_DESC"), on_change=self._on_show_settings_buttons_change),
            Switch(key="use_premium_emoji", text=locali.get_string("USE_PREMIUM_EMOJI_TITLE"), subtext=locali.get_string("USE_PREMIUM_EMOJI_SUBTEXT"), icon="menu_feature_reactions_remix", default=False),
            Divider(), Header(text=locali.get_string("DONATE_TITLE")),
            Text(text="CRYPTO [Crypto Bot]", icon="menu_cashtag", accent=True, on_click=lambda view: run_on_ui_thread(lambda: self._copy_to_clipboard("CRYPTO", "http://t.me/send?start=IVhqWW9Mz8MU"))),
            Text(text=locali.get_string("DONATE_INFO"), icon="menu_feature_reactions", accent=True, on_click=lambda view: run_on_ui_thread(lambda: get_messages_controller().openByUserName("mishabotov", get_last_fragment(), 1)))
        ]

    def _clear_cache(self, _=None):
        self.cache_manager.clear()
        message = locali.get_string("SETTINGS_CACHE_CLEARED")
        BulletinHelper.show_success(get_regular_emoji_for_bulletin(message))

    def _format_age(self, creation_date_str):
        if not creation_date_str: return "N/A"
        try: month, year = map(int, creation_date_str.split('.'))
        except: return "N/A"
        now = datetime.now()
        year_diff, month_diff = now.year - year, now.month - month
        if month_diff < 0: year_diff -= 1; month_diff += 12
        if locali.language == "ru":
            if year_diff > 0:
                y_str = f"{year_diff} год" if year_diff == 1 else (f"{year_diff} года" if 1 < year_diff < 5 else f"{year_diff} лет")
                if month_diff > 0:
                    m_str = f"{month_diff} месяц" if month_diff == 1 else (f"{month_diff} месяца" if 1 < month_diff < 5 else f"{month_diff} месяцев")
                    return f"{y_str} и {m_str}"
                return y_str
            else:
                return "менее месяца" if month_diff == 0 else (f"{month_diff} месяц" if month_diff == 1 else (f"{month_diff} месяца" if 1 < month_diff < 5 else f"{month_diff} месяцев"))
        else:
            y_str, m_str = f"{year_diff} year" + ("s" if year_diff != 1 else ""), f"{month_diff} month" + ("s" if month_diff != 1 else "")
            if year_diff > 0 and month_diff > 0: return f"{y_str} and {m_str}"
            elif year_diff > 0: return y_str
            elif month_diff > 0: return m_str
            else: return "less than a month"

    def _format_response_data(self, data, method):
        age_str = self._format_age(data.get('creation_date', ''))
        accuracy_percent = data.get('accuracy_percent', 0)
        accuracy_emoji = self.get_accuracy_emoji(accuracy_percent)
        display_username = data.get('username')
        accuracy_text = f"_{data.get('accuracy_text', 'Unknown')}_"

        account_name = get_account_name_from_telegram(str(data.get('user_id', '')))
        telegram_username = get_username_from_telegram(str(data.get('user_id', '')))

        # Получаем все юзернеймы пользователя
        user_id = str(data.get('user_id', ''))
        all_usernames = get_all_usernames_from_telegram(user_id)

        dc_id = get_user_datacenter_id(user_id)
        region = get_region_from_datacenter(dc_id)

        birthday = get_user_birthday_from_telegram(user_id)
        has_birthday = bool(birthday)

        phone_number = ""
        has_phone = False

        try:
            from client_utils import get_user_config
            current_user_id = get_user_config().getClientUserId()
            if int(user_id) != current_user_id:
                phone_number = get_user_phone_from_telegram(user_id)
                has_phone = bool(phone_number)
        except Exception as e:
            _log(f"Error checking current user: {e}")


        dc_display = str(dc_id) if dc_id > 0 else ("Неизвестно" if locali.language == "ru" else "Unknown")
        region_display = region if dc_id > 0 else ("Неизвестно" if locali.language == "ru" else "Unknown")

        # Определяем основной юзернейм для отображения
        if telegram_username:
            username_display = telegram_username
        elif all_usernames["primary"]:
            username_display = all_usernames["primary"]
        elif all_usernames["additional"]:
            username_display = all_usernames["additional"][0]  # Первый дополнительный
        elif all_usernames["collectible"]:
            username_display = all_usernames["collectible"][0]  # Первый покупной
        elif display_username and display_username.lower() != "n/a":
            username_display = display_username
        else:
            username_display = "отсутствует" if locali.language == "ru" else "not available"

        # Формируем строку с дополнительными юзернеймами (исключая основной)
        additional_usernames_str = ""
        has_additional_usernames = False

        # Собираем все юзернеймы кроме того, который показываем как основной
        all_other_usernames = []

        # Добавляем дополнительные юзернеймы (если они не являются основным)
        if all_usernames["additional"]:
            for u in all_usernames["additional"]:
                if u != username_display:
                    all_other_usernames.append(f"@{u}")

        # Добавляем покупные юзернеймы (если они не являются основным)
        if all_usernames["collectible"]:
            collectible_prefix = "💎"
            for u in all_usernames["collectible"]:
                if u != username_display:
                    all_other_usernames.append(f"{collectible_prefix}@{u}")

        # Если основной юзернейм не из telegram_username, добавляем остальные из primary
        if all_usernames["primary"] and all_usernames["primary"] != username_display:
            all_other_usernames.insert(0, f"@{all_usernames['primary']}")

        if all_other_usernames:
            has_additional_usernames = True
            additional_usernames_str = ", ".join(all_other_usernames)

        # Проверяем, есть ли у пользователя хотя бы один юзернейм
        has_any_username = (telegram_username or all_usernames["primary"] or
                           all_usernames["additional"] or all_usernames["collectible"] or
                           (display_username and display_username.lower() != "n/a"))

        if has_any_username:
            if has_additional_usernames:
                if has_birthday and has_phone:
                    result = locali.get_string("RESULT_WITH_BIRTHDAY_AND_PHONE").format(
                        data.get('user_id'), display_username, data.get('creation_date'),
                        accuracy_text, age_str, accuracy_emoji, method.capitalize(), account_name, username_display, dc_display, region_display, birthday, phone_number
                    )
                    # Добавляем дополнительные юзернеймы в конец
                    if additional_usernames_str:
                        additional_line = "🔗 Дополнительные юзернеймы:" if locali.language == "ru" else "🔗 Additional usernames:"
                        result += f"\n*{additional_line}* {additional_usernames_str}"
                elif has_birthday:
                    result = locali.get_string("RESULT_WITH_BIRTHDAY").format(
                        data.get('user_id'), display_username, data.get('creation_date'),
                        accuracy_text, age_str, accuracy_emoji, method.capitalize(), account_name, username_display, dc_display, region_display, birthday
                    )
                    # Добавляем дополнительные юзернеймы в конец
                    if additional_usernames_str:
                        additional_line = "🔗 Дополнительные юзернеймы:" if locali.language == "ru" else "🔗 Additional usernames:"
                        result += f"\n*{additional_line}* {additional_usernames_str}"
                elif has_phone:
                    result = locali.get_string("RESULT_WITH_PHONE").format(
                        data.get('user_id'), display_username, data.get('creation_date'),
                        accuracy_text, age_str, accuracy_emoji, method.capitalize(), account_name, username_display, dc_display, region_display, "", phone_number
                    )
                    # Добавляем дополнительные юзернеймы в конец
                    if additional_usernames_str:
                        additional_line = "🔗 Дополнительные юзернеймы:" if locali.language == "ru" else "🔗 Additional usernames:"
                        result += f"\n*{additional_line}* {additional_usernames_str}"
                else:
                    result = locali.get_string("RESULT").format(
                        data.get('user_id'), display_username, data.get('creation_date'),
                        accuracy_text, age_str, accuracy_emoji, method.capitalize(), account_name, username_display, dc_display, region_display
                    )
                    # Добавляем дополнительные юзернеймы в конец
                    if additional_usernames_str:
                        additional_line = "🔗 Дополнительные юзернеймы:" if locali.language == "ru" else "🔗 Additional usernames:"
                        result += f"\n*{additional_line}* {additional_usernames_str}"
            else:
                if has_birthday and has_phone:
                    result = locali.get_string("RESULT_WITH_BIRTHDAY_AND_PHONE").format(
                        data.get('user_id'), display_username, data.get('creation_date'),
                        accuracy_text, age_str, accuracy_emoji, method.capitalize(), account_name, username_display, dc_display, region_display, birthday, phone_number
                    )
                elif has_birthday:
                    result = locali.get_string("RESULT_WITH_BIRTHDAY").format(
                        data.get('user_id'), display_username, data.get('creation_date'),
                        accuracy_text, age_str, accuracy_emoji, method.capitalize(), account_name, username_display, dc_display, region_display, birthday
                    )
                elif has_phone:
                    result = locali.get_string("RESULT_WITH_PHONE").format(
                        data.get('user_id'), display_username, data.get('creation_date'),
                        accuracy_text, age_str, accuracy_emoji, method.capitalize(), account_name, username_display, dc_display, region_display, "", phone_number
                    )
                else:
                    result = locali.get_string("RESULT").format(
                        data.get('user_id'), display_username, data.get('creation_date'),
                        accuracy_text, age_str, accuracy_emoji, method.capitalize(), account_name, username_display, dc_display, region_display
                    )
        else:
            if has_birthday and has_phone:
                result = locali.get_string("RESULT_NO_USERNAME_WITH_BIRTHDAY_AND_PHONE").format(
                    data.get('user_id'), "", data.get('creation_date'),
                    accuracy_text, age_str, accuracy_emoji, method.capitalize(), account_name, username_display, dc_display, region_display, birthday, phone_number
                )
            elif has_birthday:
                result = locali.get_string("RESULT_NO_USERNAME_WITH_BIRTHDAY").format(
                    data.get('user_id'), "", data.get('creation_date'),
                    accuracy_text, age_str, accuracy_emoji, method.capitalize(), account_name, username_display, dc_display, region_display, birthday
                )
            elif has_phone:
                result = locali.get_string("RESULT_NO_USERNAME_WITH_PHONE").format(
                    data.get('user_id'), "", data.get('creation_date'),
                    accuracy_text, age_str, accuracy_emoji, method.capitalize(), account_name, username_display, dc_display, region_display, "", phone_number
                )
            else:
                result = locali.get_string("RESULT_NO_USERNAME").format(
                    data.get('user_id'), "", data.get('creation_date'),
                    accuracy_text, age_str, accuracy_emoji, method.capitalize(), account_name, username_display, dc_display, region_display
                )

        use_premium_emoji = self.get_setting("use_premium_emoji", False)
        if use_premium_emoji:
            result = replace_with_premium_emoji(result)

        return result

    def _format_free_mode_response(self, user_id: str) -> str:
        try:
            account_name = get_account_name_from_telegram(user_id)
            telegram_username = get_username_from_telegram(user_id)

            # Получаем все юзернеймы пользователя
            all_usernames = get_all_usernames_from_telegram(user_id)

            dc_id = get_user_datacenter_id(user_id)
            region = get_region_from_datacenter(dc_id)

            birthday = get_user_birthday_from_telegram(user_id)
            has_birthday = bool(birthday)

            phone_number = ""
            has_phone = False

            try:
                from client_utils import get_user_config
                current_user_id = get_user_config().getClientUserId()
                if int(user_id) != current_user_id:
                    phone_number = get_user_phone_from_telegram(user_id)
                    has_phone = bool(phone_number)
            except Exception as e:
                _log(f"Error checking current user: {e}")


            dc_display = str(dc_id) if dc_id > 0 else ("Неизвестно" if locali.language == "ru" else "Unknown")
            region_display = region if dc_id > 0 else ("Неизвестно" if locali.language == "ru" else "Unknown")

            # Определяем основной юзернейм для отображения в бесплатном режиме
            if telegram_username:
                username_display = telegram_username
            elif all_usernames["primary"]:
                username_display = all_usernames["primary"]
            elif all_usernames["additional"]:
                username_display = all_usernames["additional"][0]  # Первый дополнительный
            elif all_usernames["collectible"]:
                username_display = all_usernames["collectible"][0]  # Первый покупной
            else:
                username_display = "отсутствует" if locali.language == "ru" else "not available"

            # Формируем строку с дополнительными юзернеймами для бесплатного режима (исключая основной)
            additional_usernames_str = ""
            has_additional_usernames = False

            # Собираем все юзернеймы кроме того, который показываем как основной
            all_other_usernames_free = []

            # Добавляем дополнительные юзернеймы (если они не являются основным)
            if all_usernames["additional"]:
                for u in all_usernames["additional"]:
                    if u != username_display:
                        all_other_usernames_free.append(f"@{u}")

            # Добавляем покупные юзернеймы (если они не являются основным)
            if all_usernames["collectible"]:
                collectible_prefix = "💎"
                for u in all_usernames["collectible"]:
                    if u != username_display:
                        all_other_usernames_free.append(f"{collectible_prefix}@{u}")

            # Если основной юзернейм не из telegram_username, добавляем остальные из primary
            if all_usernames["primary"] and all_usernames["primary"] != username_display:
                all_other_usernames_free.insert(0, f"@{all_usernames['primary']}")

            if all_other_usernames_free:
                has_additional_usernames = True
                additional_usernames_str = ", ".join(all_other_usernames_free)

            # Проверяем, есть ли у пользователя хотя бы один юзернейм в бесплатном режиме
            has_any_username_free = (telegram_username or all_usernames["primary"] or
                                   all_usernames["additional"] or all_usernames["collectible"])

            if has_any_username_free:
                if has_additional_usernames:
                    if has_birthday and has_phone:
                        result = locali.get_string("FREE_MODE_RESULT_WITH_BIRTHDAY_AND_PHONE").format(
                            account_name, user_id, dc_display, region_display, username_display, phone_number, birthday
                        )
                    elif has_birthday:
                        result = locali.get_string("FREE_MODE_RESULT_WITH_BIRTHDAY").format(
                            account_name, user_id, dc_display, region_display, username_display, birthday
                        )
                    elif has_phone:
                        result = locali.get_string("FREE_MODE_RESULT_WITH_PHONE").format(
                            account_name, user_id, dc_display, region_display, username_display, phone_number
                        )
                    else:
                        result = locali.get_string("FREE_MODE_RESULT").format(
                            account_name, user_id, dc_display, region_display, username_display
                        )
                    # Добавляем дополнительные юзернеймы в конец для бесплатного режима
                    if additional_usernames_str:
                        additional_line = "🔗 Дополнительные юзернеймы:" if locali.language == "ru" else "🔗 Additional usernames:"
                        result += f"\n*{additional_line}* {additional_usernames_str}"
                else:
                    if has_birthday and has_phone:
                        result = locali.get_string("FREE_MODE_RESULT_WITH_BIRTHDAY_AND_PHONE").format(
                            account_name, user_id, dc_display, region_display, username_display, phone_number, birthday
                        )
                    elif has_birthday:
                        result = locali.get_string("FREE_MODE_RESULT_WITH_BIRTHDAY").format(
                            account_name, user_id, dc_display, region_display, username_display, birthday
                        )
                    elif has_phone:
                        result = locali.get_string("FREE_MODE_RESULT_WITH_PHONE").format(
                            account_name, user_id, dc_display, region_display, username_display, phone_number
                        )
                    else:
                        result = locali.get_string("FREE_MODE_RESULT").format(
                            account_name, user_id, dc_display, region_display, username_display
                        )
            else:
                if has_birthday and has_phone:
                    result = locali.get_string("FREE_MODE_NO_USERNAME_WITH_BIRTHDAY_AND_PHONE").format(
                        account_name, user_id, dc_display, region_display, phone_number, birthday
                    )
                elif has_birthday:
                    result = locali.get_string("FREE_MODE_NO_USERNAME_WITH_BIRTHDAY").format(
                        account_name, user_id, dc_display, region_display, birthday
                    )
                elif has_phone:
                    result = locali.get_string("FREE_MODE_NO_USERNAME_WITH_PHONE").format(
                        account_name, user_id, dc_display, region_display, phone_number
                    )
                else:
                    result = locali.get_string("FREE_MODE_NO_USERNAME").format(
                        account_name, user_id, dc_display, region_display
                    )

            use_premium_emoji = self.get_setting("use_premium_emoji", False)
            if use_premium_emoji:
                result = replace_with_premium_emoji(result)

            return result

        except Exception as e:
            _log(f"Error formatting free mode response: {e}")
            error_message = locali.get_string("CODE_ERROR").format(str(e))
            return error_message

    def _process_age_request(self, params, initial_target, method):
        try:
            canonical_id = None
            if isinstance(initial_target, str) and not initial_target.isdigit():
                resolve_result = self.api_client.resolve_username(initial_target)
                if resolve_result["success"]:
                    canonical_id = str(resolve_result["data"]["id"])
                else:
                    error_msg = locali.get_string("API_ERROR").format(resolve_result["error"])
                    run_on_ui_thread(lambda: self._reply_with_formatted_text(params, error_msg))
                    return
            else:
                canonical_id = str(initial_target)

            cached_data = self.cache_manager.get(canonical_id, method)
            if cached_data:
                message = self._format_response_data(cached_data, method)
                cached_message = f"{locali.get_string('CACHED_RESULT_HINT')}\n{message}"
                run_on_ui_thread(lambda: self._reply_with_formatted_text(params, cached_message))
            else:
                result = self.api_client.get_info(canonical_id, method)
                if not result["success"]:
                    message = locali.get_string("API_ERROR").format(result["error"])
                else:
                    raw_data = result["data"]
                    message = self._format_response_data(raw_data, method)
                    self.cache_manager.set(canonical_id, method, raw_data)
                run_on_ui_thread(lambda: self._reply_with_formatted_text(params, message))

        except Exception as e:
            _log(f"Error in processing thread! {traceback.format_exc()}")
            error_message = locali.get_string("CODE_ERROR").format(str(e))
            run_on_ui_thread(lambda: self._reply_with_formatted_text(params, error_message))
        finally:
            self.is_processing = False
            self._dismiss_loading_dialog()

    def _process_free_mode_request(self, params, initial_target):
        try:
            canonical_id = None

            if isinstance(initial_target, str) and not initial_target.isdigit():
                try:
                    from client_utils import get_messages_controller
                    user_or_chat = get_messages_controller().getUserOrChat(initial_target.lstrip('@'))
                    if user_or_chat and hasattr(user_or_chat, 'id') and not hasattr(user_or_chat, 'title'):
                        canonical_id = str(user_or_chat.id)
                    else:
                        error_msg = f"⚠️ В бесплатном режиме невозможно найти пользователя @{initial_target.lstrip('@')}. Используйте ID пользователя или включите [API режим](https://t.me/dateregbot?start=api)."
                        use_premium_emoji = self.get_setting("use_premium_emoji", False)
                        if use_premium_emoji:
                            error_msg = replace_with_premium_emoji(error_msg)
                        run_on_ui_thread(lambda: self._reply_with_formatted_text(params, error_msg))
                        return
                except Exception as e:
                    _log(f"Error finding user by username in free mode: {e}")
                    error_msg = f"⚠️ В бесплатном режиме невозможно найти пользователя @{initial_target.lstrip('@')}. Используйте ID пользователя или включите [API режим](https://t.me/dateregbot?start=api)."
                    use_premium_emoji = self.get_setting("use_premium_emoji", False)
                    if use_premium_emoji:
                        error_msg = replace_with_premium_emoji(error_msg)
                    run_on_ui_thread(lambda: self._reply_with_formatted_text(params, error_msg))
                    return
            else:
                canonical_id = str(initial_target)

            message = self._format_free_mode_response(canonical_id)
            run_on_ui_thread(lambda: self._reply_with_formatted_text(params, message))

        except Exception as e:
            _log(f"Error in free mode processing thread! {traceback.format_exc()}")
            error_message = locali.get_string("CODE_ERROR").format(str(e))
            run_on_ui_thread(lambda: self._reply_with_formatted_text(params, error_message))
        finally:
            self.is_processing = False
            self._dismiss_loading_dialog()

    def on_send_message_hook(self, account, params) -> HookResult:
        if not hasattr(params, "message") or not isinstance(params.message, str):
            return HookResult()

        message_text = params.message.strip()
        peer_id = getattr(params, "peer", None)
        if not peer_id: return HookResult()

        if message_text.startswith(".cache "):
            parts = message_text.split(" ", 1)
            if len(parts) < 2:
                message = locali.get_string("CACHE_DELETE_USAGE")
                BulletinHelper.show_error(get_regular_emoji_for_bulletin(message))
                return HookResult(strategy=HookStrategy.CANCEL)

            target_id = parts[1].strip()
            if self.cache_manager.delete(target_id):
                message = locali.get_string("CACHE_DELETE_SUCCESS").format(target_id)
                BulletinHelper.show_success(get_regular_emoji_for_bulletin(message))
            else:
                message = locali.get_string("CACHE_DELETE_NOT_FOUND").format(target_id)
                BulletinHelper.show_info(get_regular_emoji_for_bulletin(message))

            return HookResult(strategy=HookStrategy.CANCEL)

        if not message_text.startswith(".age"):
            return HookResult()

        if self.is_processing:
            message = locali.get_string("PROCESSING")
            BulletinHelper.show_info(get_regular_emoji_for_bulletin(message))
            return HookResult(strategy=HookStrategy.CANCEL)

        try:
            parts = message_text.split(" ", 2)
            if len(parts) > 1 and parts[1].strip().lower() == locali.get_string("FAQ_COMMAND"):
                self._show_info_alert(locali.get_string("FAQ_TITLE"), locali.get_string("FAQ_CONTENT"))
                return HookResult(strategy=HookStrategy.CANCEL)

            free_mode = self.get_setting("free_mode", False)

            if free_mode:
                use_smart = len(parts) > 1 and parts[1].lower() == "smart"
                target = parts[2].strip() if use_smart and len(parts) > 2 else (parts[1].strip() if len(parts) > 1 and not use_smart else None)

                if not target:
                    target = self._get_user_id_from_reply(params)

                if not target:
                    usage_msg = locali.get_string("USAGE_EXAMPLE")
                    use_premium_emoji = self.get_setting("use_premium_emoji", False)
                    if use_premium_emoji:
                        usage_msg = replace_with_premium_emoji(usage_msg)
                    self._reply_with_formatted_text(params, usage_msg)
                    return HookResult(strategy=HookStrategy.CANCEL)

                self.is_processing = True
                self._show_loading_dialog()
                run_on_queue(lambda: self._process_free_mode_request(params, target))
            else:
                if not self.api_client:
                    self._reply_with_formatted_text(params, locali.get_string("NO_API_KEY"))
                    return HookResult(strategy=HookStrategy.CANCEL)

                use_smart = len(parts) > 1 and parts[1].lower() == "smart"
                target = parts[2].strip() if use_smart and len(parts) > 2 else (parts[1].strip() if len(parts) > 1 and not use_smart else None)

                if not target:
                    target = self._get_user_id_from_reply(params)

                if not target:
                    usage_msg = locali.get_string("USAGE_EXAMPLE")
                    use_premium_emoji = self.get_setting("use_premium_emoji", False)
                    if use_premium_emoji:
                        usage_msg = replace_with_premium_emoji(usage_msg)
                    self._reply_with_formatted_text(params, usage_msg)
                    return HookResult(strategy=HookStrategy.CANCEL)

                method = "smart" if use_smart else "fast"

                self.is_processing = True
                self._show_loading_dialog()
                run_on_queue(lambda: self._process_age_request(params, target, method))

            return HookResult(strategy=HookStrategy.CANCEL)

        except Exception as e:
            self.is_processing = False
            self._dismiss_loading_dialog()
            _log(f"Error in hook! {traceback.format_exc()}")
            error_message = locali.get_string("CODE_ERROR").format(str(e))
            self._reply_with_formatted_text(params, error_message)
            return HookResult(strategy=HookStrategy.CANCEL)

    def _copy_to_clipboard(self, label, text):
        from org.telegram.messenger import AndroidUtilities
        if AndroidUtilities.addToClipboard(text):
            message = f"Copied {label} to clipboard"
            BulletinHelper.show_info(get_regular_emoji_for_bulletin(message))

    def _open_plugin_settings(self, java_plugin):
        try: get_last_fragment().presentFragment(PluginSettingsActivity(java_plugin))
        except Exception as e: _log(f"Error opening plugin settings: {e}")

    def _add_settings_menu_items(self):
        try:
            if not self._drawer_settings_item: self._drawer_settings_item = self.add_menu_item(MenuItemData(menu_type=MenuItemType.DRAWER_MENU, text=locali.get_string("SETTINGS_TITLE"), icon="msg_settings_14", priority=5, on_click=lambda ctx: run_on_ui_thread(lambda: self._open_plugin_settings(PluginsController.getInstance().plugins.get(self.id)))))
            if not self._chat_settings_item: self._chat_settings_item = self.add_menu_item(MenuItemData(menu_type=MenuItemType.CHAT_ACTION_MENU, text=locali.get_string("SETTINGS_TITLE"), icon="msg_settings_14", priority=5, on_click=lambda ctx: run_on_ui_thread(lambda: self._open_plugin_settings(PluginsController.getInstance().plugins.get(self.id)))))
        except Exception as e: _log(f"Failed to add settings menu items: {e}")

    def _on_show_settings_buttons_change(self, enabled: bool):
        def _toggle():
            try:
                if enabled: self._add_settings_menu_items()
                else:
                    if self._drawer_settings_item: self.remove_menu_item(self._drawer_settings_item); self._drawer_settings_item = None
                    if self._chat_settings_item: self.remove_menu_item(self._chat_settings_item); self._chat_settings_item = None
            except Exception as e: _log(f"Failed toggling settings buttons: {e}")
        run_on_ui_thread(_toggle)

    def get_accuracy_emoji(self, accuracy_percent):
        if not isinstance(accuracy_percent, (int, float)): return "❓"
        if accuracy_percent >= 87: return "🟢"
        elif accuracy_percent >= 70: return "🟡"
        elif accuracy_percent >= 50: return "🟠"
        else: return "🔴"

    def _get_user_id_from_reply(self, params):
        if hasattr(params, "replyToMsg") and params.replyToMsg:
            msg_owner = params.replyToMsg.messageOwner
            if msg_owner and hasattr(msg_owner, "from_id") and msg_owner.from_id and hasattr(msg_owner.from_id, "user_id"):
                return str(msg_owner.from_id.user_id)
        return None

    def _reply_with_formatted_text(self, params, markdown_text):
        try:
            parsed = parse_markdown(markdown_text)
            message_payload = {
                "peer": params.peer,
                "message": parsed.text,
                "entities": [e.to_tlrpc_object() for e in parsed.entities],
                "replyToMsg": getattr(params, "replyToMsg", None),
                "replyToTopMsg": getattr(params, "replyToTopMsg", None)
            }
            send_message(message_payload)
        except Exception as e:
            _log(f"Markdown parse/send error: {e}")
            plain_text = markdown_text.replace("*", "").replace("`", "").replace("_", "")
            message_payload = {
                "peer": params.peer,
                "message": plain_text,
                "replyToMsg": getattr(params, "replyToMsg", None),
                "replyToTopMsg": getattr(params, "replyToTopMsg", None)
            }
            send_message(message_payload)

    def _show_info_alert(self, title, message):
        fragment = get_last_fragment()
        if not fragment: return
        builder = AlertDialogBuilder(fragment.getParentActivity())
        builder.set_title(title)
        builder.set_message(message)
        builder.set_positive_button("Close", None)
        run_on_ui_thread(builder.show)

    def _show_loading_dialog(self):
        fragment = get_last_fragment()
        if not fragment or self.alert_dialog: return
        run_on_ui_thread(lambda: self.__show_loading_dialog_on_ui(fragment.getParentActivity()))

    def __show_loading_dialog_on_ui(self, context):
        self.alert_dialog = AlertDialog(context, 3)
        self.alert_dialog.setCanCancel(False)
        self.alert_dialog.show()
        self.alert_dialog.setTitle(locali.get_string("LOADING_TITLE"))

    def _dismiss_loading_dialog(self):
        def dismiss_action():
            if self.alert_dialog:
                try:
                    self.alert_dialog.dismiss()
                except Exception as e:
                    _log(f"Error dismissing dialog: {e}")
                finally:
                    self.alert_dialog = None
        run_on_ui_thread(dismiss_action)

    def _handle_profile_info_click(self, context):
        try:
            user = context.get("user")
            if not user:
                _log("User not found in profile context")
                return

            user_id = str(user.id)
            _log(f"Profile info button clicked for user: {user.first_name} (ID: {user_id})")

            self._show_profile_info_dialog(user_id)

        except Exception as e:
            _log(f"Error in profile info click handler: {str(e)}")
            self._show_error_dialog(f"Ошибка: {str(e)}")

    def _show_profile_info_dialog(self, user_id: str):
        try:
            free_mode = self.get_setting("free_mode", True)

            if free_mode or not self.api_client:
                info_text = self._format_profile_info_for_dialog(user_id, None)
                title = "📊 Информация об аккаунте (локальные данные)"
                self._show_info_alert(title, info_text)
            else:
                self._show_loading_dialog()
                run_on_queue(lambda: self._fetch_profile_info_with_api(user_id))

        except Exception as e:
            _log(f"Error showing profile info dialog: {str(e)}")
            self._show_error_dialog(f"Ошибка получения информации: {str(e)}")

    def _fetch_profile_info_with_api(self, user_id: str):
        try:
            cached_data = self.cache_manager.get(user_id, "fast")

            if cached_data:
                _log(f"Using cached data for profile dialog: {user_id}")
                run_on_ui_thread(lambda: self._show_profile_info_with_api_data(user_id, cached_data, True))
                return

            _log(f"Making API request for profile dialog: {user_id}")
            api_result = self.api_client.get_info(user_id, "fast")

            if api_result["success"]:
                self.cache_manager.set(user_id, "fast", api_result["data"])
                run_on_ui_thread(lambda: self._show_profile_info_with_api_data(user_id, api_result["data"], False))
            else:
                _log(f"API error for profile dialog: {api_result['error']}")
                run_on_ui_thread(lambda: self._show_profile_info_api_error(user_id, api_result["error"]))

        except Exception as e:
            _log(f"Exception in _fetch_profile_info_with_api: {str(e)}")
            run_on_ui_thread(lambda: self._show_profile_info_api_error(user_id, str(e)))
        finally:
            run_on_ui_thread(lambda: self._dismiss_loading_dialog())

    def _show_profile_info_with_api_data(self, user_id: str, api_data: dict, from_cache: bool):
        try:
            info_text = self._format_profile_info_for_dialog(user_id, api_data)
            cache_hint = " (из кэша)" if from_cache else ""
            title = f"📊 Информация об аккаунте{cache_hint}"
            self._show_info_alert(title, info_text)
        except Exception as e:
            _log(f"Error showing profile info with API data: {str(e)}")
            self._show_error_dialog(f"Ошибка отображения информации: {str(e)}")

    def _show_profile_info_api_error(self, user_id: str, error_message: str):
        try:
            info_text = self._format_profile_info_for_dialog(user_id, None)
            title = "📊 Информация об аккаунте (локальные данные)"
            error_note = f"\n\n⚠️ Ошибка API: {error_message}"
            self._show_info_alert(title, info_text + error_note)
        except Exception as e:
            _log(f"Error showing profile info with API error: {str(e)}")
            self._show_error_dialog(f"Ошибка отображения информации: {str(e)}")

    def _format_profile_info_for_dialog(self, user_id: str, api_data: dict = None) -> str:
        try:
            account_name = get_account_name_from_telegram(user_id)
            telegram_username = get_username_from_telegram(user_id)

            dc_id = get_user_datacenter_id(user_id)
            region = get_region_from_datacenter(dc_id)

            birthday = get_user_birthday_from_telegram(user_id)
            has_birthday = bool(birthday)

            phone_number = ""
            has_phone = False

            try:
                from client_utils import get_user_config
                current_user_id = get_user_config().getClientUserId()
                if int(user_id) != current_user_id:
                    phone_number = get_user_phone_from_telegram(user_id)
                    has_phone = bool(phone_number)
            except Exception as e:
                _log(f"Error checking current user: {e}")

            dc_display = str(dc_id) if dc_id > 0 else ("Неизвестно" if locali.language == "ru" else "Unknown")
            region_display = region if dc_id > 0 else ("Неизвестно" if locali.language == "ru" else "Unknown")

            if telegram_username:
                username_display = f"@{telegram_username}"
            else:
                username_display = "отсутствует" if locali.language == "ru" else "not available"

            info_lines = []
            info_lines.append(f"👤 Имя: {account_name}")
            info_lines.append(f"🆔 ID: {user_id}")
            info_lines.append(f"🔗 Юзернейм: {username_display}")

            if has_phone:
                info_lines.append(f"📱 Телефон: {phone_number}")

            if has_birthday:
                info_lines.append(f"🎂 День рождения: {birthday}")

            if api_data and api_data.get('creation_date'):
                creation_date = api_data.get('creation_date')
                age_str = self._format_age(creation_date)
                accuracy_percent = api_data.get('accuracy_percent', 0)
                accuracy_emoji = self.get_accuracy_emoji(accuracy_percent)
                accuracy_text = api_data.get('accuracy_text', 'Unknown')

                info_lines.append(f"📅 Дата создания: {creation_date}")
                info_lines.append(f"⏳ Возраст аккаунта: {age_str}")
                info_lines.append(f"🎯 Точность: {accuracy_emoji} {accuracy_text}")

            info_lines.append(f"🖥 Датацентр: {dc_display}")
            info_lines.append(f"🌍 Регион: {region_display}")

            result = "\n".join(info_lines)

            return result

        except Exception as e:
            _log(f"Error formatting profile info for dialog: {e}")
            return f"Ошибка форматирования информации: {str(e)}"

    def _show_error_dialog(self, message: str):
        title = "❌ Ошибка"
        self._show_info_alert(title, message)